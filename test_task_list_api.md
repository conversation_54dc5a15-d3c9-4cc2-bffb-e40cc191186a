# Test GraphQL API cho Task List

## 1. Query TaskListByType - <PERSON><PERSON><PERSON> sách task theo loại

### Query c<PERSON> bản (tất cả task)
```graphql
query {
  taskListByType {
    success
    message
    data {
      userLevelInfo {
        currentLevel
        currentLevelName
        progressToNextLevel
        pointsToNextLevel
        totalPoints
        levelBenefits
        nextLevelBenefits
      }
      todayStats {
        completedToday
        totalDailyTasks
        pointsEarnedToday
        streakCount
        canClaimDailyBonus
      }
      dailyTasks {
        taskType
        displayName
        completedCount
        totalCount
        totalPointsAvailable
        totalPointsEarned
        tasks {
          task {
            id
            name
            description
            points
            taskType
            taskIdentifier
            isActive
          }
          progress {
            id
            status
            progressValue
            targetValue
            completionCount
            pointsEarned
            progressPercentage
            canBeClaimed
          }
          completionStatus
          isClaimable
          canComplete
          nextResetTime
          remainingAttempts
          progressPercentage
        }
      }
      communityTasks {
        taskType
        displayName
        completedCount
        totalCount
        totalPointsAvailable
        totalPointsEarned
        tasks {
          task {
            id
            name
            description
            points
            taskType
            taskIdentifier
            externalLink
          }
          progress {
            status
            progressValue
            progressPercentage
            canBeClaimed
          }
          completionStatus
          isClaimable
          canComplete
        }
      }
      tradingTasks {
        taskType
        displayName
        completedCount
        totalCount
        totalPointsAvailable
        totalPointsEarned
        tasks {
          task {
            id
            name
            description
            points
            taskType
            conditions
          }
          progress {
            status
            progressValue
            targetValue
            progressPercentage
          }
          completionStatus
          canComplete
        }
      }
    }
  }
}
```

### Query với filter - Chỉ lấy task chưa hoàn thành
```graphql
query {
  taskListByType(filter: {
    completionStatus: NOT_COMPLETED
  }) {
    success
    message
    data {
      dailyTasks {
        tasks {
          task {
            name
            points
          }
          completionStatus
          canComplete
        }
      }
      communityTasks {
        tasks {
          task {
            name
            points
            externalLink
          }
          completionStatus
          canComplete
        }
      }
      tradingTasks {
        tasks {
          task {
            name
            points
          }
          completionStatus
          canComplete
        }
      }
    }
  }
}
```

### Query với filter - Chỉ lấy task đã hoàn thành
```graphql
query {
  taskListByType(filter: {
    completionStatus: COMPLETED
  }) {
    success
    message
    data {
      dailyTasks {
        completedCount
        totalPointsEarned
        tasks {
          task {
            name
            points
          }
          progress {
            pointsEarned
            lastCompletedAt
          }
          completionStatus
        }
      }
      communityTasks {
        completedCount
        totalPointsEarned
        tasks {
          task {
            name
            points
          }
          progress {
            pointsEarned
            lastCompletedAt
          }
          completionStatus
        }
      }
      tradingTasks {
        completedCount
        totalPointsEarned
        tasks {
          task {
            name
            points
          }
          progress {
            pointsEarned
            lastCompletedAt
          }
          completionStatus
        }
      }
    }
  }
}
```

## 2. Query FilteredTaskList - Lấy task với filter chi tiết

### Filter theo loại task
```graphql
query {
  filteredTaskList(filter: {
    taskType: DAILY
    completionStatus: ALL
  }) {
    success
    message
    data {
      categories {
        category {
          name
          displayName
        }
        tasks {
          task {
            name
            points
            taskType
          }
          progress {
            status
            progressPercentage
          }
        }
      }
      completedToday
      pointsEarnedToday
    }
  }
}
```

### Filter theo trạng thái hoàn thành
```graphql
query {
  filteredTaskList(filter: {
    completionStatus: CLAIMABLE
  }) {
    success
    message
    data {
      categories {
        tasks {
          task {
            name
            points
          }
          progress {
            status
            canBeClaimed
          }
        }
      }
    }
  }
}
```

## 3. So sánh với TaskCenter hiện tại

### TaskCenter query hiện tại
```graphql
query {
  taskCenter {
    success
    message
    data {
      categories {
        category {
          name
          displayName
        }
        tasks {
          task {
            id
            name
            points
            taskType
          }
          progress {
            status
            progressValue
            progressPercentage
          }
        }
      }
      completedToday
      pointsEarnedToday
      streakTasks {
        streakCount
      }
    }
  }
}
```

## 4. Test Cases để kiểm tra

1. **Test cơ bản**: Query taskListByType không có filter
2. **Test filter theo task type**: DAILY, COMMUNITY, TRADING
3. **Test filter theo completion status**: COMPLETED, NOT_COMPLETED, IN_PROGRESS, CLAIMABLE
4. **Test user level info**: Kiểm tra thông tin level và progress
5. **Test daily stats**: Kiểm tra thống kê hàng ngày
6. **Test task grouping**: Kiểm tra task được group đúng theo type
7. **Test progress calculation**: Kiểm tra tính toán progress percentage
8. **Test reset time**: Kiểm tra thời gian reset task
9. **Test remaining attempts**: Kiểm tra số lần còn lại có thể làm task

## 5. Expected Response Structure

Response sẽ có cấu trúc phù hợp với màn hình mobile:
- **Tab 1 (My Level)**: `userLevelInfo` + `dailyTasks` 
- **Tab 2 (Community Tasks)**: `communityTasks`
- **Tab 3 (Trading Tasks)**: `tradingTasks`
- **Stats**: `todayStats` cho thống kê tổng quan
