# Task List GraphQL API - Summary

## Tổng quan

Đã tạo thành công GraphQL API mới để phục vụ màn hình Task List trên mobile app, được tối ưu hóa cho việc hiển thị task theo từng loại (Daily, Community, Trading) như trong thiết kế UI.

## API Endpoints mới

### 1. `taskListByType(filter: TaskListFilter): TaskListResponse!`

**Mục đích**: API chính cho màn hình Task List, trả về data được tổ chức theo 3 tab:
- **Tab 1 - My Level**: Daily tasks + User level info
- **Tab 2 - Community Tasks**: Social media tasks  
- **Tab 3 - Trading Tasks**: Volume-based trading tasks

**Đặc điểm**:
- Group tasks theo TaskType (DAILY, COMMUNITY, TRADING)
- Tính toán statistics cho từng group (completed count, total points)
- Bao gồm user level info và daily stats
- Hỗ trợ filtering theo completion status

### 2. `filteredTaskList(filter: TaskListFilter!): TaskCenterResponse!`

**<PERSON><PERSON><PERSON> đích**: API linh hoạt để filter tasks theo nhiều tiêu chí khác nhau

**Đặc điểm**:
- Filter theo TaskType, CompletionStatus, CategoryName
- Trả về format tương tự TaskCenter hiện tại
- Phù hợp cho các use case đặc biệt

## Input Types

### TaskListFilter
```graphql
input TaskListFilter {
  taskType: TaskType                    # DAILY, COMMUNITY, TRADING
  completionStatus: TaskCompletionStatus # ALL, COMPLETED, NOT_COMPLETED, IN_PROGRESS, CLAIMABLE
  categoryName: String                  # Filter theo tên category
  includeInactive: Boolean = false      # Có bao gồm task inactive không
}
```

### TaskCompletionStatus
```graphql
enum TaskCompletionStatus {
  ALL
  COMPLETED
  NOT_COMPLETED  
  IN_PROGRESS
  CLAIMABLE
}
```

## Response Types

### TaskListByType
```graphql
type TaskListByType {
  dailyTasks: TaskTypeGroup!        # Tab 1 - Daily tasks
  communityTasks: TaskTypeGroup!    # Tab 2 - Community tasks  
  tradingTasks: TaskTypeGroup!      # Tab 3 - Trading tasks
  userLevelInfo: UserLevelSummary!  # User level info cho Tab 1
  todayStats: DailyTaskStats!       # Statistics tổng quan
}
```

### TaskTypeGroup
```graphql
type TaskTypeGroup {
  taskType: TaskType!
  displayName: String!
  tasks: [TaskWithProgressAndStatus!]!
  completedCount: Int!              # Số task đã hoàn thành
  totalCount: Int!                  # Tổng số task
  totalPointsAvailable: Int!        # Tổng points có thể earn
  totalPointsEarned: Int!           # Tổng points đã earn
}
```

### TaskWithProgressAndStatus
```graphql
type TaskWithProgressAndStatus {
  task: ActivityTask!
  progress: UserTaskProgress
  completionStatus: TaskCompletionStatus!
  isClaimable: Boolean!
  canComplete: Boolean!
  nextResetTime: Time               # Thời gian reset task tiếp theo
  remainingAttempts: Int            # Số lần còn lại có thể làm task
  progressPercentage: Float!
  estimatedCompletionTime: Time     # Dự đoán thời gian hoàn thành
}
```

### UserLevelSummary
```graphql
type UserLevelSummary {
  currentLevel: Int!
  currentLevelName: String!         # Bronze, Silver, Gold, etc.
  progressToNextLevel: Float!       # % progress to next level
  pointsToNextLevel: Int!
  totalPoints: Int!
  levelBenefits: [String!]!         # Benefits của level hiện tại
  nextLevelBenefits: [String!]!     # Benefits của level tiếp theo
}
```

### DailyTaskStats
```graphql
type DailyTaskStats {
  completedToday: Int!
  totalDailyTasks: Int!
  pointsEarnedToday: Int!
  streakCount: Int!                 # Streak cao nhất
  canClaimDailyBonus: Boolean!      # Có thể claim bonus hàng ngày
}
```

## Mapping với UI Design

### Tab 1 - 我的等级 (My Level)
- **Data source**: `taskListByType.userLevelInfo` + `taskListByType.dailyTasks`
- **Level progress bar**: `userLevelInfo.progressToNextLevel`
- **Daily tasks**: `dailyTasks.tasks[]` với filter `taskType: DAILY`

### Tab 2 - 社区任务 (Community Tasks)  
- **Data source**: `taskListByType.communityTasks`
- **Social tasks**: Follow Twitter, Retweet, Like, Join Telegram, etc.
- **External links**: `task.externalLink` cho các social media links

### Tab 3 - 交易任务 (Trading Tasks)
- **Data source**: `taskListByType.tradingTasks`  
- **Volume tasks**: $10K, $50K, $100K, $500K trading volume
- **Progress tracking**: `progress.progressValue` vs `progress.targetValue`

## Ưu điểm của API mới

1. **Tối ưu cho Mobile UI**: Data được tổ chức sẵn theo tab structure
2. **Giảm số lượng request**: 1 query duy nhất cho toàn bộ màn hình
3. **Rich metadata**: Bao gồm completion status, reset time, remaining attempts
4. **Flexible filtering**: Hỗ trợ nhiều cách filter khác nhau
5. **Performance**: Tính toán statistics ở backend thay vì frontend
6. **Extensible**: Dễ dàng thêm fields mới khi cần

## Backward Compatibility

- API cũ (`taskCenter`) vẫn hoạt động bình thường
- Có thể migrate dần từ API cũ sang API mới
- Cùng sử dụng chung data models và business logic

## Testing

- Đã tạo test queries trong `test_queries.graphql`
- Đã tạo test cases trong `test_task_list_api.md`
- API đã compile thành công và sẵn sàng test

## Next Steps

1. Test API với real data
2. Integrate với mobile app
3. Monitor performance và optimize nếu cần
4. Thêm caching layer nếu cần thiết
5. Migrate từ API cũ sang API mới dần dần
