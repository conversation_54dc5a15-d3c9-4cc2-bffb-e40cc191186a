# Test queries for Task List API

# Query 1: Basic TaskListByType - Get all tasks organized by type
query GetTaskListByType {
  taskListByType {
    success
    message
    data {
      userLevelInfo {
        currentLevel
        currentLevelName
        progressToNextLevel
        pointsToNextLevel
        totalPoints
      }
      todayStats {
        completedToday
        totalDailyTasks
        pointsEarnedToday
        streakCount
        canClaimDailyBonus
      }
      dailyTasks {
        taskType
        displayName
        completedCount
        totalCount
        totalPointsAvailable
        totalPointsEarned
        tasks {
          task {
            id
            name
            points
            taskIdentifier
          }
          completionStatus
          isClaimable
          canComplete
          progressPercentage
        }
      }
      communityTasks {
        taskType
        displayName
        completedCount
        totalCount
        tasks {
          task {
            id
            name
            points
            externalLink
          }
          completionStatus
          isClaimable
          canComplete
        }
      }
      tradingTasks {
        taskType
        displayName
        completedCount
        totalCount
        tasks {
          task {
            id
            name
            points
          }
          completionStatus
          canComplete
          progressPercentage
        }
      }
    }
  }
}

# Query 2: Filter for incomplete tasks only
query GetIncompleteTasksByType {
  taskListByType(filter: {
    completionStatus: NOT_COMPLETED
  }) {
    success
    message
    data {
      dailyTasks {
        completedCount
        totalCount
        tasks {
          task {
            name
            points
          }
          canComplete
        }
      }
      communityTasks {
        completedCount
        totalCount
        tasks {
          task {
            name
            points
            externalLink
          }
          canComplete
        }
      }
      tradingTasks {
        completedCount
        totalCount
        tasks {
          task {
            name
            points
          }
          canComplete
        }
      }
    }
  }
}

# Query 3: Filter for completed tasks only
query GetCompletedTasksByType {
  taskListByType(filter: {
    completionStatus: COMPLETED
  }) {
    success
    message
    data {
      dailyTasks {
        completedCount
        totalPointsEarned
        tasks {
          task {
            name
            points
          }
          progress {
            pointsEarned
            lastCompletedAt
          }
        }
      }
      communityTasks {
        completedCount
        totalPointsEarned
        tasks {
          task {
            name
            points
          }
          progress {
            pointsEarned
            lastCompletedAt
          }
        }
      }
      tradingTasks {
        completedCount
        totalPointsEarned
        tasks {
          task {
            name
            points
          }
          progress {
            pointsEarned
            lastCompletedAt
          }
        }
      }
    }
  }
}

# Query 4: Filter for daily tasks only
query GetDailyTasksOnly {
  filteredTaskList(filter: {
    taskType: DAILY
  }) {
    success
    message
    data {
      categories {
        category {
          name
          displayName
        }
        tasks {
          task {
            name
            points
            taskType
            taskIdentifier
          }
          progress {
            status
            progressValue
            progressPercentage
          }
        }
      }
      completedToday
      pointsEarnedToday
    }
  }
}

# Query 5: Filter for claimable tasks
query GetClaimableTasks {
  filteredTaskList(filter: {
    completionStatus: CLAIMABLE
  }) {
    success
    message
    data {
      categories {
        tasks {
          task {
            name
            points
          }
          progress {
            status
            canBeClaimed
            pointsEarned
          }
          isClaimable
        }
      }
    }
  }
}

# Query 6: Compare with existing TaskCenter
query GetTaskCenter {
  taskCenter {
    success
    message
    data {
      categories {
        category {
          name
          displayName
        }
        tasks {
          task {
            id
            name
            points
            taskType
          }
          progress {
            status
            progressValue
            progressPercentage
          }
        }
      }
      completedToday
      pointsEarnedToday
      streakTasks {
        streakCount
      }
    }
  }
}

# Query 7: Get user dashboard for comparison
query GetUserDashboard {
  activityCashbackDashboard {
    success
    message
    data {
      userTierInfo {
        currentTier
        totalPoints
        claimableCashbackUsd
      }
      pointsToNextTier
      userRank
    }
  }
}
