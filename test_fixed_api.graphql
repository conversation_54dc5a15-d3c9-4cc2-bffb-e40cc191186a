# Test queries for fixed TaskListByType API

# Test 1: Query only DAILY tasks
query GetDailyTasksOnly {
  taskListByType(filter: { taskType: DAILY }) {
    success
    message
    data {
      # Should only return dailyTasks, others should be null
      dailyTasks {
        taskType
        displayName
        completedCount
        totalCount
        totalPointsAvailable
        totalPointsEarned
        tasks {
          task {
            id
            name
            points
            taskType
            taskIdentifier
          }
          completionStatus
          isClaimable
          canComplete
          progressPercentage
        }
      }
      # These should be null when filtering for DAILY
      communityTasks {
        taskType
        displayName
        completedCount
        totalCount
      }
      tradingTasks {
        taskType
        displayName
        completedCount
        totalCount
      }
      userLevelInfo {
        currentLevel
        currentLevelName
        totalPoints
      }
      todayStats {
        completedToday
        totalDailyTasks
        pointsEarnedToday
        streakCount
      }
    }
  }
}

# Test 2: Query only COMMUNITY tasks
query GetCommunityTasksOnly {
  taskListByType(filter: { taskType: COMMUNITY }) {
    success
    message
    data {
      # Should be null when filtering for COMMUNITY
      dailyTasks {
        taskType
        completedCount
        totalCount
      }
      # Should only return communityTasks
      communityTasks {
        taskType
        displayName
        completedCount
        totalCount
        totalPointsAvailable
        totalPointsEarned
        tasks {
          task {
            id
            name
            points
            taskType
            externalLink
          }
          completionStatus
          isClaimable
          canComplete
        }
      }
      # Should be null when filtering for COMMUNITY
      tradingTasks {
        taskType
        completedCount
        totalCount
      }
      userLevelInfo {
        currentLevel
        currentLevelName
        totalPoints
      }
      todayStats {
        completedToday
        pointsEarnedToday
      }
    }
  }
}

# Test 3: Query only TRADING tasks
query GetTradingTasksOnly {
  taskListByType(filter: { taskType: TRADING }) {
    success
    message
    data {
      # Should be null when filtering for TRADING
      dailyTasks {
        taskType
        completedCount
        totalCount
      }
      communityTasks {
        taskType
        completedCount
        totalCount
      }
      # Should only return tradingTasks
      tradingTasks {
        taskType
        displayName
        completedCount
        totalCount
        totalPointsAvailable
        totalPointsEarned
        tasks {
          task {
            id
            name
            points
            taskType
            conditions
          }
          completionStatus
          canComplete
          progressPercentage
        }
      }
      userLevelInfo {
        currentLevel
        currentLevelName
        totalPoints
      }
      todayStats {
        completedToday
        pointsEarnedToday
      }
    }
  }
}

# Test 4: Query DAILY tasks with completion status filter
query GetCompletedDailyTasks {
  taskListByType(filter: { 
    taskType: DAILY,
    completionStatus: COMPLETED 
  }) {
    success
    message
    data {
      dailyTasks {
        taskType
        displayName
        completedCount
        totalCount
        tasks {
          task {
            name
            points
          }
          progress {
            status
            pointsEarned
            lastCompletedAt
          }
          completionStatus
        }
      }
      # These should be null
      communityTasks {
        completedCount
        totalCount
      }
      tradingTasks {
        completedCount
        totalCount
      }
    }
  }
}

# Test 5: Query COMMUNITY tasks with NOT_COMPLETED filter
query GetIncompleteCommunityTasks {
  taskListByType(filter: { 
    taskType: COMMUNITY,
    completionStatus: NOT_COMPLETED 
  }) {
    success
    message
    data {
      # Should be null
      dailyTasks {
        completedCount
        totalCount
      }
      communityTasks {
        taskType
        displayName
        completedCount
        totalCount
        tasks {
          task {
            name
            points
            externalLink
          }
          completionStatus
          canComplete
        }
      }
      # Should be null
      tradingTasks {
        completedCount
        totalCount
      }
    }
  }
}

# Test 6: Verify no circular reference in UserTaskProgress
query TestNoCircularReference {
  taskListByType(filter: { taskType: DAILY }) {
    success
    data {
      dailyTasks {
        tasks {
          task {
            id
            name
            # userProgress should not have task field to avoid circular reference
            userProgress {
              id
              status
              progressValue
              progressPercentage
              # task field should not exist here
            }
          }
          progress {
            id
            status
            progressValue
            progressPercentage
            # task field should not exist here either
          }
        }
      }
    }
  }
}

# Expected Results:
# - Test 1: Only dailyTasks should have data, others null
# - Test 2: Only communityTasks should have data, others null  
# - Test 3: Only tradingTasks should have data, others null
# - Test 4: Only completed daily tasks should be returned
# - Test 5: Only incomplete community tasks should be returned
# - Test 6: No circular reference errors should occur
